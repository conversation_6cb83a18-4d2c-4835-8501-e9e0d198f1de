<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="Advanced encryption and decryption tool supporting multiple cipher algorithms including Caesar, <PERSON>igen<PERSON>, ROT13, Base64, and Atbash.">
    <title>Encryption Suite</title>
    <link rel="shortcut icon" href="assets/favicon.png" type="image/x-icon" />
    <link rel="stylesheet" href="style.css" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body data-theme="dark">
    <div class="container">
        <header>
            <div class="header-content">
                <h1>Encryption Suite</h1>
                <p class="subtitle">Secure your data with multiple encryption algorithms</p>
                <button id="themeToggle" class="theme-toggle" title="Toggle theme">🌙</button>
            </div>
        </header>

        <main>
            <div class="controls-section">
                <!-- Algorithm Selection and Settings -->
                <div class="control-group">
                    <!-- Algorithm and Settings Row -->
                    <div class="algorithm-settings-row">
                        <div class="algorithm-section">
                            <label for="algorithmSelect">Encryption Algorithm</label>
                            <select id="algorithmSelect" class="compact-select">
                                <option value="caesar">Caesar Cipher</option>
                                <option value="vigenere">Vigenère Cipher</option>
                                <option value="rot13">ROT13</option>
                                <option value="base64">Base64 Encoding</option>
                                <option value="atbash">Atbash Cipher</option>
                            </select>
                        </div>

                        <!-- Settings Panel -->
                        <div class="setting-item">
                            <label class="toggle-label">
                                <input type="checkbox" id="realTimeToggle">
                                <span class="toggle-slider"></span>
                                Real-time Processing
                            </label>
                        </div>
                    </div>

                    <!-- Key/Shift Inputs -->
                    <div class="key-inputs">
                        <div class="control-group">
                            <label for="inputShift">Shift Value</label>
                            <input id="inputShift" type="number" placeholder="Enter shift value (1-25)" min="1" max="25" value="3" />
                        </div>

                        <div id="keyInputContainer" class="control-group" style="display: none;">
                            <label for="keyInput">Encryption Key</label>
                            <input id="keyInput" type="text" placeholder="Enter encryption key" />
                        </div>
                    </div>

                    <!-- Info Panel -->
                    <div class="info-panel-compact">
                        <h4>🛡️ Algorithm Information</h4>
                        <div id="algorithmInfo" class="algorithm-info">
                            <p><strong>Caesar Cipher:</strong> A simple substitution cipher where each letter is shifted by a fixed number of positions in the alphabet.</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="content-grid">
                <div class="input-column">
                    <div class="text-section">
                        <!-- Input Section -->
                        <div class="input-section">
                            <div class="input-header">
                                <label for="inputText">Input Text</label>
                                <div class="input-actions">
                                    <button type="button" onclick="encryptionApp?.uploadFile()" class="action-btn" title="Upload file">📁</button>
                                    <button type="button" onclick="encryptionApp?.clearAll()" class="btn btn-utility" title="Clear all fields">
                                🗑️
                            </button>
                                    <span id="charCount" class="char-count">0 characters</span>
                                </div>
                            </div>
                            <textarea id="inputText" rows="12" placeholder="Enter your text here..." spellcheck="false" autocapitalize="none" autofocus></textarea>
                            
                            <div class="action-buttons">
                                <button type="button" onclick="encryptData()" class="btn btn-primary">
                                    🔒 Encrypt
                                </button>
                                <button type="button" onclick="decryptData()" class="btn btn-secondary">
                                    🔓 Decrypt
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="output-column">
                    <div class="text-section">
                        <!-- Output Section -->
                        <div class="output-section">
                            <div class="output-header">
                                <label for="outputText">Output</label>
                                <div class="output-actions">
                                    <button type="button" onclick="encryptionApp?.copyToClipboard('outputText')" class="action-btn" title="Copy to clipboard">📋</button>
                                    <button type="button" onclick="encryptionApp?.swapInputOutput()" class="btn btn-utility" title="Swap input and output">
                                🔄
                            </button>
                                    <span id="outputCharCount" class="char-count">0 characters</span>
                                </div>
                            </div>
                            <textarea id="outputText" rows="12" placeholder="Encrypted/decrypted text will appear here..." spellcheck="false" readonly></textarea>
                            
                        </div>
                    </div>
                </div>
            </div>

            <div class="third-row">
                <div class="third-column">
                    <div class="text-section">
                        <!-- Third Section -->
                        <div class="third-section">
                            <div class="third-header">
                                <label for="thirdText">Analysis / History</label>
                                <div class="third-actions">
                                    <button type="button" onclick="encryptionApp?.copyToClipboard('thirdText')" class="action-btn" title="Copy to clipboard">📋</button>
                                    <button type="button" onclick="encryptionApp?.clearThirdColumn()" class="action-btn" title="Clear analysis">🗑️</button>
                                </div>
                            </div>
                            <textarea id="thirdText" rows="8" placeholder="Analysis results, operation history, or additional processing will appear here..." spellcheck="false" readonly></textarea>
                        </div>
                    </div>
                </div>
            </div>

        </main>
    </div>

    <script src="script.js"></script>
</body>
</html>
